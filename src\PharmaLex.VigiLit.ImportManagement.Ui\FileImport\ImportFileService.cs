﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Domain.Exceptions;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Documents;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using DownloadFile = PharmaLex.VigiLit.ImportManagement.Ui.Models.DownloadFile;

namespace PharmaLex.VigiLit.ImportManagement.Ui.FileImport;
internal class ImportFileService : IImportFileService
{
    private readonly IImportFileRepository _importFileRepository;
    private readonly IImportFileDocumentService _importFileDocumentUploadService;
    private readonly IMapper _mapper;
    private readonly IDataExtractionClient _dataExtractionClient;
    private readonly IImportRepository _importRepository;
    private readonly TimeProvider _timeProvider;

    public ImportFileService(
        IImportFileRepository importFileRepository,
        IImportFileDocumentService importFileDocumentUploadService,
        IMapper mapper,
        IDataExtractionClient dataExtractionClient, IImportRepository importRepository, TimeProvider timeProvider)
    {
        _importFileRepository = importFileRepository;
        _importFileDocumentUploadService = importFileDocumentUploadService;
        _mapper = mapper;
        _dataExtractionClient = dataExtractionClient;
        _importRepository = importRepository;
        _timeProvider = timeProvider;
    }

    public async Task Add(ImportFileUploadModel model)
    {
        if (model == null)
        {
            throw new ArgumentNullException(nameof(model), "Model cannot be null.");
        }

        if (model.Stream == null)
        {
            throw new ArgumentNullException(nameof(model), "File stream cannot be null.");
        }
        var importFile = _mapper.Map<ImportFile>(model.ImportFile);

        try
        {
            _importFileRepository.Add(importFile);
            await _importFileRepository.SaveChangesAsync();
            await UploadImportFileDocument(importFile.BatchId, importFile.FileName, model.Stream);
            var fileProperties = await GetImportFileDocumentProperties(importFile.BatchId, importFile.FileName);

            if (fileProperties == null)
            {
                throw new InvalidOperationException("Failed to retrieve file properties after upload.");
            }

            importFile.FileSize = (int)fileProperties.FileSize;
            await _importFileRepository.SaveChangesAsync();
        }

        catch (DbUpdateException ex)
        {
            _importFileRepository.Remove(importFile);
            await _importFileRepository.SaveChangesAsync();
            throw new DbUpdateException("Database update exception raised.", ex);
        }

        catch (Exception ex)
        {
            _importFileRepository.Remove(importFile);
            await _importFileRepository.SaveChangesAsync();
            throw new InvalidOperationException("Import file upload failed.", ex);
        }
    }

    private async Task UploadImportFileDocument(Guid batchId, string fileName, Stream stream)
    {
        var importFileDocumentUploadDescriptor = new ImportFileUploadDescriptor(batchId, fileName);

        await _importFileDocumentUploadService.Create(importFileDocumentUploadDescriptor, stream);

    }

    private async Task<DocumentProperties> GetImportFileDocumentProperties(Guid batchId, string fileName)
    {
        var importFileDocumentUploadDescriptor = new ImportFileUploadDescriptor(batchId, fileName);
        return await _importFileDocumentUploadService.GetDocumentProperties(importFileDocumentUploadDescriptor);
    }

    public async Task<List<ImportFileModel>> GetImportFile(Guid batchId)
    {
        var files = await _importFileRepository.GetImportFilesByBatchId(batchId);
        return _mapper.Map<List<ImportFileModel>>(files);
    }

    public async Task Enqueue(Guid batchId)
    {
        var importFiles = await GetImportFile(batchId);
        if (importFiles == null || importFiles.Count == 0)
        {
            throw new KeyNotFoundException($"No import files found for batch ID: {batchId}");
        }
        foreach (var file in importFiles)
        {
            var command = new ExtractDataCommand
            {
                CorrelationId = Guid.NewGuid(),
                BatchId = file.BatchId.ToString(),
                FileName = file.FileName,
                Source = Source.File,
            };
            await SaveImport(command.CorrelationId);
            await _dataExtractionClient.Send(command);
        }
        await DeleteImportFiles(importFiles);
    }

    private async Task SaveImport(Guid commandCorrelationId)
    {
        var import = new Import
        {
            ImportType = ImportType.File,
            CorrelationId = commandCorrelationId,
            ImportStatusType = ImportStatusType.Queued,
            ImportTriggerType = ImportTriggerType.Manual,
            StartDate = _timeProvider.GetUtcNow().UtcDateTime,
            ImportDate = _timeProvider.GetUtcNow().UtcDateTime,
            Message = "Queued"
        };
        _importRepository.Add(import);
        await _importRepository.SaveChangesAsync();
    }

    public async Task Abandon(Guid batchId)
    {
        var importFiles = await _importFileRepository.GetImportFilesByBatchId(batchId);

        if (importFiles == null || !importFiles.Any())
        {
            throw new KeyNotFoundException($"No import files found for batch ID: {batchId}");
        }

        foreach (var file in importFiles)
        {
            await DeleteImportFileDocument(file.FileName, batchId);
            _importFileRepository.Remove(file);
        }
        await _importFileRepository.SaveChangesAsync();
    }
    public async Task DeleteImportFiles(List<ImportFileModel> files)
    {
        var errors = new List<string>();

        foreach (var file in files)
        {
            try
            {
                await DeleteImportFile(file);
            }
            catch (Exception)
            {
                errors.Add($"Failed to delete {file.FileName} (Batch: {file.BatchId})");
            }
        }

        if (errors.Count != 0)
        {
            throw new InvalidOperationException($"Some files could not be deleted: {string.Join(", ", errors)}");
        }
    }

    public async Task<DownloadFile> DownloadImportedFile(Guid batchId, string fileName)
    {
        var pdfBytes = await RetrieveDocument(batchId, fileName);
        return new DownloadFile
        {
            FileName = fileName,
            ContentType = "application/pdf",
            Bytes = pdfBytes
        };
    }

    public async Task<DownloadFile> DownloadFile(Guid batchId, string fileName)
    {
        var pdfBytes = await RetrieveImportedDocument(batchId, fileName);
        return new DownloadFile
        {
            FileName = fileName,
            ContentType = "application/pdf",
            Bytes = pdfBytes
        };
    }

    public async Task DeleteImportFile(ImportFileModel model)
    {
        try
        {
            var file = await _importFileRepository.GetImportFile(model.FileName, model.BatchId);
            _importFileRepository.Remove(file);
            await _importFileRepository.SaveChangesAsync();
            await DeleteImportFileDocument(file.FileName, file.BatchId);

        }
        catch (KeyNotFoundException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"deleting {model.FileName} failed.", ex);
        }
    }


    private async Task DeleteImportFileDocument(string fileName, Guid batchId)
    {
        var importFileDocumentUploadDescriptor = new ImportFileUploadDescriptor(batchId, fileName);
        var exists = await _importFileDocumentUploadService.Exists(importFileDocumentUploadDescriptor);
        if (!exists)
        {
            throw new NotFoundException("Import File does not exist.");
        }

        await _importFileDocumentUploadService.Delete(importFileDocumentUploadDescriptor);

    }

    public async Task<List<ImportDisplayCard>> GetCards()
    {
        const int maxFilesOnCard = 2;

        var displayCards = new List<ImportDisplayCard>();
        var fileImports = await GetAllFileImports();
        foreach (var import in fileImports.GroupBy(x => x.BatchId))
        {
            var createdDate = import.OrderBy(x => x.CreatedDate).ToList();
            var updatedBy = import.OrderByDescending(x => x.LastUpdatedDate).ToList();
            displayCards.Add(new ImportDisplayCard()
            {
                Id = import.Key.ToString(),
                ImportType = "File",
                Title = "",
                Filename = "",
                DateFrom = "",
                DateTo = "",
                FileCount = import.Count(),
                Files = import.Select(x => x.FileName ?? string.Empty).Take(maxFilesOnCard).ToList(),
                FilesPlus = import.Count() - maxFilesOnCard < 0 ? 0 : import.Count() - maxFilesOnCard,
                CreatedBy = createdDate[0].CreatedBy ?? "",
                LastUpdatedBy = updatedBy[0].LastUpdatedBy ?? "",
                FailedImportStatus = 0
            });
        }

        return displayCards;
    }

    private async Task<List<ImportFileModel>> GetAllFileImports()
    {
        var files = await _importFileRepository.GetAll();
        return _mapper.Map<List<ImportFileModel>>(files);
    }

    private async Task<byte[]> RetrieveDocument(Guid batchId, string fileName)
    {
        var importFileDocumentUploadDescriptor = new ImportFileUploadDescriptor(batchId, fileName);
        using var stream = await _importFileDocumentUploadService.OpenRead(importFileDocumentUploadDescriptor);

        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream);

        return memoryStream.ToArray();
    }

    private async Task<byte[]> RetrieveImportedDocument(Guid batchId, string fileName)
    {
        var importFileDocumentDescriptor = new ImportFileDescriptor(batchId, fileName);
        using var stream = await _importFileDocumentUploadService.OpenRead(importFileDocumentDescriptor);

        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream);

        return memoryStream.ToArray();
    }
}
