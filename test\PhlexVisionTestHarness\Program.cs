﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.DataExtraction.Service;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.ImportManagement.Client;
using PharmaLex.VigiLit.ImportManagement.Service;
using System.Reflection;
using System.Text.Json;
using Microsoft.Extensions.Time.Testing;
using PharmaLex.VigiLit.Auditing.Client;

namespace PhlexVisionTestHarness;

internal static class Program
{
    private const string CorrelationIdHeader = "X-Correlation-ID";

    private static CallbackHandler? _responseHandler;
    private static ServiceProvider? _provider;
    private static IConfiguration? _configuration;

#pragma warning disable S2190
    static async Task Main(string[] args)
    {
        _configuration = new ConfigurationBuilder()
            .AddUserSecrets(Assembly.GetExecutingAssembly())
            .Build();

        var services = new ServiceCollection();
        services.AddHttpClient();
        services.RegisterImportManagementClient();
        services.RegisterImportManagement(_configuration);
        services.AddSingleton<IConfiguration>(_configuration);
        services.AddScoped<PlxDbContext, VigiLitDbContext>();
        services.RegisterDbContext<VigiLitDbContext>();
        services.AddScoped<IUserContext, TestUserContext>();
        services.AddScoped<IVigiLitUserContext, TestUserContext>();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

        services.AddLogging(configure =>
        {
            configure.AddConsole();
        });
        _provider = services.BuildServiceProvider();


        StartListener();
        Console.WriteLine("Press Enter to send request...");
        Console.ReadLine();

        while (true)
        {
            await CallPhlexVisionService();

            Console.WriteLine("Waiting for response... Press Enter to exit...");
            Console.ReadLine();
        }
    }
#pragma warning restore S2190

    private static void StartListener()
    {
        _ = Task.Run(StartCallbackListener);
    }

    static async Task CallPhlexVisionService()
    {
        var httpClientFactory = _provider!.GetService<IHttpClientFactory>();
        var logger = _provider!.GetService<ILogger<PhlexVisionService>>();
        var auditClient = _provider!.GetService<IAuditClient>();

        var phlexVisionService = new PhlexVisionService(logger!, httpClientFactory!, _configuration!, auditClient!);

        await phlexVisionService.RequestDataExtraction(new ExtractRequest
        {
            BatchId = "VigiLit",
            FileName = "https://cayman-much-shot-ltd.trycloudflare.com/download"
            // DocumentLocation = "https://as-phlexvision-int-eun.azurewebsites.net/api/mock/v1/Ocr/document/Correia_F_2024"
        });
    }
    static readonly JsonSerializerOptions jsonOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    static void StartCallbackListener()
    {
        var builder = WebApplication.CreateBuilder();
        var app = builder.Build();

        var responseLogger = _provider!.GetService<ILogger<CallbackHandler>>();

        var importManagementClient = _provider!.GetService<IImportManagementClient>();

        var repo = _provider!.GetService<IMdeQueueItemRepository>();

        var fileDownload = _provider!.GetService<IExtractDataFileDownload>();

        var configuration = _provider!.GetService<IConfiguration>();

        var extractionValidator = _provider!.GetService<ICompositeQualityChecker>();

        var extractionProfile = _provider!.GetService<IDataExtractionProfile>();

        var auditClient = _provider!.GetService<IAuditClient>();

        var fakeTimeProvider = new FakeTimeProvider();

        _responseHandler = new CallbackHandler(responseLogger!, importManagementClient!, fileDownload!, repo!, configuration!, extractionValidator!, extractionProfile!, auditClient!, fakeTimeProvider);

        app.MapPost("/error", async (HttpContext context) =>
        {
            using var reader = new StreamReader(context.Request.Body);
            var body = await reader.ReadToEndAsync();
            Console.WriteLine("Received Error callback:");
            Console.WriteLine(body);
            return Results.Ok("Callback Error received");
        });

        app.MapPost("/success", async (HttpContext context) =>
        {
            var correlationIdHeader = context.Request.Headers[CorrelationIdHeader];
            if (!Guid.TryParse(correlationIdHeader, out var correlationId))
            {
                return Results.BadRequest("Invalid or missing Correlation ID");
            }
            ExtractedMetadata? extractedMetadata;
            try
            {
                extractedMetadata = await JsonSerializer.DeserializeAsync<ExtractedMetadata>(context.Request.Body, jsonOptions);

                if (extractedMetadata == null)
                {
                    return Results.BadRequest("Request body is empty or invalid JSON");
                }
            }
            catch (JsonException)
            {
                return Results.BadRequest($"Failed to parse JSON");
            }

            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine(correlationId);
            Console.ForegroundColor = ConsoleColor.White;

            var contextInfo = new ContextInfo()
            {
                Language = "eng",
                RawTranslatedText = ""
            };

            await _responseHandler.Success(correlationId, extractedMetadata, contextInfo);

            return Results.Ok("Callback Success received");
        });

#pragma warning disable CS1998
        app.MapGet("/download", async (HttpContext context) =>
        {
            var correlationId = context.Request.Headers[CorrelationIdHeader];

            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine(correlationId);
            Console.ForegroundColor = ConsoleColor.White;

            var bytes = File.ReadAllBytes("TestData\\TESTDATA2.PDF");
            return Results.Stream(new MemoryStream(bytes), "application/pdf", "filename.pdf");

        });
#pragma warning restore CS1998

#pragma warning disable S1075 
        // This is where we will host for testing purposes
        app.Run("https://localhost:8080");
#pragma warning restore S1075 
    }
}