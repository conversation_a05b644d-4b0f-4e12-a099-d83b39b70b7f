using Phlex.Core.Apify.Webhook.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public interface IApifyMetadataService
{
    Dictionary<string, string> ExtractMetadataFromWebhookPayload(ApifyWebhookPayload runData);

    Task<Dictionary<string, List<string>>> GroupFilesByJournalAsync(IEnumerable<string> blobPaths, Azure.Storage.Blobs.BlobContainerClient containerClient);

    Task<Dictionary<string, List<string>>> GroupFilesByJournalUsingKeyValueStoreAsync(string runId, CancellationToken cancellationToken = default);
}
