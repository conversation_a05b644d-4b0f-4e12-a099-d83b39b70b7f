﻿using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Services;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;
using Task = System.Threading.Tasks.Task;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests;

public class ApifyNotificationServiceTest
{
    private readonly IApifyNotification _notificationService;
    private readonly Mock<ILogger<ApifyNotification>> _mockLogger = new();
    private readonly Mock<IApifyClient> _apifyClient = new();
    private readonly Mock<IDownloadBlobStorage> _blobStorage = new();
    private readonly Mock<IDataExtractionClient> _client = new();
    private readonly Mock<IApifyMetadataService> _metadataService = new();

    public ApifyNotificationServiceTest()
    {
        _notificationService = new ApifyNotification(_apifyClient.Object, _blobStorage.Object, _client.Object, _metadataService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task RunSucceedMethod_ValidPayload()
    {
        var actorTaskId = Fake.GetRandomString(5);
        var mockFilePaths = new List<string>
        {
            "scraping/folder1/file1.pdf",
            "scraping/folder1/file2.txt"
        };
        var payload = new ApifyWebhookPayload()
        {
            createdAt = DateTime.UtcNow,
            resource = new Resource()
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = Fake.GetRandomString(5),
                defaultKeyValueStoreId = Fake.GetRandomString(5)
            }
        };

        _blobStorage.Setup(x => x.GetBlobPathsAsync(
           actorTaskId,
           It.IsAny<CancellationToken>()
       ))
       .ReturnsAsync(mockFilePaths);

        _blobStorage.Setup(x => x.GetContainerClient())
            .Returns((Azure.Storage.Blobs.BlobContainerClient)null!);

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
          .Returns(Task.CompletedTask);

        _metadataService.Setup(x => x.ExtractMetadataFromWebhookPayload(It.IsAny<ApifyWebhookPayload>()))
            .Returns(new Dictionary<string, string> { { "ActorTaskId", actorTaskId } });

        // Setup the new method to return grouped files
        _metadataService.Setup(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, List<string>> { { "Default", mockFilePaths.ToList() } });

        // Setup fallback method in case the new method fails
        _metadataService.Setup(x => x.GroupFilesByJournalAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<Azure.Storage.Blobs.BlobContainerClient>()))
            .ReturnsAsync(new Dictionary<string, List<string>> { { "Default", mockFilePaths.ToList() } });

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _blobStorage.Verify(x => x.SetBlobFolderName(actorTaskId), Times.Once());
        _apifyClient.Verify(
            x => x.TransferFilesAsync(
                payload.resource.defaultDatasetId,
                payload.resource.defaultKeyValueStoreId,
                _blobStorage.Object,
                It.IsAny<IReadOnlyDictionary<string, string>>(),
                CancellationToken.None
            ),
            Times.Once()
        );

        /// Basic verification that Send was called twice
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()),
                     Times.Exactly(2));

        // More detailed verification
        foreach (var filePath in mockFilePaths)
        {
            _client.Verify(x => x.Send(It.Is<ExtractDataCommand>(cmd =>
                cmd.FileName == filePath &&
                cmd.Source == Source.File
            )), Times.Once());
        }
    }

    [Fact]
    public async Task RunSucceeded_WithValidData_SendsCorrectExtractDataCommands()
    {
        // Arrange
        var actorTaskId = Fake.GetRandomString(8);
        var runData = new ApifyWebhookPayload
        {
            resource = new Resource
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = Fake.GetRandomString(10),
                defaultKeyValueStoreId = Fake.GetRandomString(10)
            }
        };

        var blobPaths = new[]
        {
        $"scraping/{actorTaskId}/file1.pdf",
        $"scraping/{actorTaskId}/file2.pdf"
    };

        _blobStorage.Setup(x => x.GetBlobPathsAsync(
           actorTaskId,
           It.IsAny<CancellationToken>()
       ))
       .ReturnsAsync(blobPaths);

        _blobStorage.Setup(x => x.GetContainerClient())
            .Returns((Azure.Storage.Blobs.BlobContainerClient)null!);

        _metadataService.Setup(x => x.ExtractMetadataFromWebhookPayload(It.IsAny<ApifyWebhookPayload>()))
            .Returns(new Dictionary<string, string> { { "ActorTaskId", "task123" } });

        // Setup the new method to throw an exception so it falls back to the old method
        _metadataService.Setup(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Key-value store method not implemented"));

        _metadataService.Setup(x => x.GroupFilesByJournalAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<Azure.Storage.Blobs.BlobContainerClient>()))
            .ReturnsAsync(new Dictionary<string, List<string>> { { "Default", blobPaths.ToList() } });

        var sentCommands = new List<ExtractDataCommand>();
        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
            .Callback<ExtractDataCommand>(cmd => sentCommands.Add(cmd))
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(runData);

        // Assert
        Assert.Equal(2, sentCommands.Count);

        // Verify all commands share the same BatchId
        var batchId = sentCommands[0].BatchId;
        Assert.All(sentCommands, cmd => Assert.Equal(batchId, cmd.BatchId));

        // Verify each command has the correct FileName
        Assert.Contains(sentCommands, cmd => cmd.FileName == $"scraping/{actorTaskId}/file1.pdf");
        Assert.Contains(sentCommands, cmd => cmd.FileName == $"scraping/{actorTaskId}/file2.pdf");

        // Verify Source is set correctly
        Assert.All(sentCommands, cmd => Assert.Equal(Source.File, cmd.Source));

        var file1Command = sentCommands.First(cmd => cmd.FileName == $"scraping/{actorTaskId}/file1.pdf");
        var file2Command = sentCommands.First(cmd => cmd.FileName == $"scraping/{actorTaskId}/file2.pdf");

        _client.Verify(x => x.Send(
            It.Is<ExtractDataCommand>(cmd =>
                cmd.FileName == file1Command.FileName &&
                cmd.Source == file1Command.Source &&
                cmd.BatchId == file1Command.BatchId &&
                cmd.CorrelationId == file1Command.CorrelationId)),
            Times.Once);

        _client.Verify(x => x.Send(
            It.Is<ExtractDataCommand>(cmd =>
                cmd.FileName == file2Command.FileName &&
                cmd.Source == file2Command.Source &&
                cmd.BatchId == file2Command.BatchId &&
                cmd.CorrelationId == file2Command.CorrelationId)),
            Times.Once);

        // Verify CorrelationIds are unique
        var correlationIds = sentCommands.Select(cmd => cmd.CorrelationId).ToList();
        Assert.Equal(correlationIds.Count, correlationIds.Distinct().Count());
    }

    [Theory]
    [InlineData("", "XYZ")]
    [InlineData("XYZ", "")]
    [InlineData("", "")]
    [InlineData(null, null)]
    public async Task RunSucceedMethod_InvalidPayload_DoesNotCallTransferFiles(string defaultDatasetId, string defaultKeyValueStoreId)
    {
        var actorTaskId = Fake.GetRandomString(5);
        var payload = new ApifyWebhookPayload()
        {
            createdAt = DateTime.UtcNow,
            resource = new Resource()
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = defaultDatasetId,
                defaultKeyValueStoreId = defaultKeyValueStoreId
            }
        };

        await _notificationService.RunSucceeded(payload);

        _blobStorage.Verify(x => x.SetBlobFolderName(actorTaskId), Times.Never);

        _apifyClient.Verify(
            x => x.TransferFilesAsync(
                payload.resource.defaultDatasetId,
                payload.resource.defaultKeyValueStoreId,
                _blobStorage.Object,
                It.IsAny<IReadOnlyDictionary<string, string>>(),
                CancellationToken.None
            ),
            Times.Never
        );

        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()),
                    Times.Never);
    }

    [Fact]
    public async Task RunSucceedMethod_EmptyFolder_DoesNotCallSend()
    {
        // Arrange
        var payload = new ApifyWebhookPayload { resource = new Resource() };
        _blobStorage.Setup(x => x.GetBlobPathsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync([]);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Never());
    }

    [Fact]
    public async Task RunFailed_ValidPayload_LogsError()
    {
        // Arrange
        var actorTaskId = Fake.GetRandomString(5);
        var payload = new ApifyWebhookPayload()
        {
            createdAt = DateTime.UtcNow,
            resource = new Resource()
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = Fake.GetRandomString(5),
                defaultKeyValueStoreId = Fake.GetRandomString(5)
            }
        };

        // Act
        await _notificationService.RunFailed(payload);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v != null && v.ToString()!.Contains("Scraping run failure")),
                It.IsAny<Exception?>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Fact]
    public async Task RunFailed_NullPayload_LogsError()
    {
        // Act
        await _notificationService.RunFailed(new ApifyWebhookPayload());

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v != null && v.ToString()!.Contains("ApifyNotification: RunFailed called with insufficient run data")),
                It.IsAny<Exception?>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task RunFailed_NullResource_LogsError()
    {
        // Arrange
        var payload = new ApifyWebhookPayload()
        {
            createdAt = DateTime.UtcNow,
            resource = null
        };

        // Act
        await _notificationService.RunFailed(payload);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v != null && v.ToString()!.Contains("ApifyNotification: RunFailed called with insufficient run data")),
                It.IsAny<Exception?>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task RunSucceeded_UsesKeyValueStoreMethod_WhenAvailable()
    {
        // Arrange
        var actorTaskId = Fake.GetRandomString(8);
        var mockFilePaths = new List<string> { "file1.pdf", "file2.pdf" };
        var payload = new ApifyWebhookPayload()
        {
            createdAt = DateTime.UtcNow,
            resource = new Resource()
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = Fake.GetRandomString(5),
                defaultKeyValueStoreId = Fake.GetRandomString(5)
            }
        };

        _metadataService.Setup(x => x.ExtractMetadataFromWebhookPayload(It.IsAny<ApifyWebhookPayload>()))
            .Returns(new Dictionary<string, string> { { "ActorTaskId", actorTaskId } });

        _metadataService.Setup(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(actorTaskId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, List<string>> { { "TestJournal", mockFilePaths } });

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _metadataService.Verify(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(actorTaskId, It.IsAny<CancellationToken>()), Times.Once);
        _metadataService.Verify(x => x.GroupFilesByJournalAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<Azure.Storage.Blobs.BlobContainerClient>()), Times.Never);
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Exactly(2));
    }

    [Fact]
    public async Task RunSucceeded_FallsBackToBlobMethod_WhenKeyValueStoreMethodFails()
    {
        // Arrange
        var actorTaskId = Fake.GetRandomString(8);
        var mockFilePaths = new List<string> { "scraping/folder/file1.pdf", "scraping/folder/file2.pdf" };
        var payload = new ApifyWebhookPayload()
        {
            createdAt = DateTime.UtcNow,
            resource = new Resource()
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = Fake.GetRandomString(5),
                defaultKeyValueStoreId = Fake.GetRandomString(5)
            }
        };

        _metadataService.Setup(x => x.ExtractMetadataFromWebhookPayload(It.IsAny<ApifyWebhookPayload>()))
            .Returns(new Dictionary<string, string> { { "ActorTaskId", actorTaskId } });

        // Setup key-value store method to throw exception
        _metadataService.Setup(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(actorTaskId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Key-value store error"));

        // Setup fallback blob method
        _blobStorage.Setup(x => x.GetBlobPathsAsync(actorTaskId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockFilePaths);
        _blobStorage.Setup(x => x.GetContainerClient())
            .Returns((Azure.Storage.Blobs.BlobContainerClient)null!);
        _metadataService.Setup(x => x.GroupFilesByJournalAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<Azure.Storage.Blobs.BlobContainerClient>()))
            .ReturnsAsync(new Dictionary<string, List<string>> { { "Default", mockFilePaths } });

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
            .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _metadataService.Verify(x => x.GroupFilesByJournalUsingKeyValueStoreAsync(actorTaskId, It.IsAny<CancellationToken>()), Times.Once);
        _metadataService.Verify(x => x.GroupFilesByJournalAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<Azure.Storage.Blobs.BlobContainerClient>()), Times.Once);
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Exactly(2));
    }
}
