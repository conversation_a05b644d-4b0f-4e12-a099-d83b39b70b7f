using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Services;
using PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Builders;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Services;

public class ApifyTaskServiceTests
{
    private readonly Mock<IApifyClient> _apifyClientMock;
    private readonly Mock<ILogger<ApifyTaskService>> _loggerMock;
    private readonly ApifyTaskService _sut;

    public ApifyTaskServiceTests()
    {
        _apifyClientMock = new Mock<IApifyClient>();
        _loggerMock = new Mock<ILogger<ApifyTaskService>>();
        _sut = new ApifyTaskService(_apifyClientMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task CreateTaskForJournalAsync_WhenSuccessful_ReturnsTaskId()
    {
        // Arrange
        var journal = new JournalScheduleInfoBuilder().WithName("Nature Medicine").WithUrl("https://nature.com/nm").Build();
        var taskName = Fake.GetRandomString(20);
        var expectedTaskId = Fake.GetRandomString(10);

        _apifyClientMock.Setup(x => x.CreateTaskAsync(taskName, It.IsAny<List<string>>(), 1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedTaskId);

        // Act
        var result = await _sut.CreateTaskForJournalAsync(journal, taskName);

        // Assert
        Assert.Equal(expectedTaskId, result);

        _apifyClientMock.Verify(x => x.CreateTaskAsync(
            taskName,
            It.Is<List<string>>(urls => urls.Count == 1 && urls[0] == journal.Url),
            1,
            It.IsAny<CancellationToken>()), Times.Once);

        VerifyLogCalled(LogLevel.Information, "Created Apify task");
    }

    [Fact]
    public async Task CreateTaskForJournalAsync_WhenApifyClientThrows_ThrowsInvalidOperationException()
    {
        // Arrange
        var journal = new JournalScheduleInfoBuilder().WithName("Nature Medicine").WithUrl("https://nature.com/nm").Build();
        var taskName = Fake.GetRandomString(20);
        var originalException = new Exception("Apify API error");

        _apifyClientMock.Setup(x => x.CreateTaskAsync(It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(originalException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _sut.CreateTaskForJournalAsync(journal, taskName));

        Assert.Contains($"Failed to create Apify task '{taskName}' for journal 'Nature Medicine'", exception.Message);
        Assert.Contains("URL: https://nature.com/nm", exception.Message);
        Assert.Equal(originalException, exception.InnerException);

        VerifyLogCalled(LogLevel.Error, "Failed to create Apify task");
    }

    [Fact]
    public async Task CreateTaskForJournalAsync_WithSpecialCharactersInJournalName_HandlesCorrectly()
    {
        // Arrange
        var journal = new JournalScheduleInfoBuilder().WithName("Journal of Clinical Oncology (JCO)").WithUrl("https://test.org").Build();
        var taskName = Fake.GetRandomString(25);
        var expectedTaskId = Fake.GetRandomString(10);

        _apifyClientMock.Setup(x => x.CreateTaskAsync(taskName, It.IsAny<List<string>>(), 1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedTaskId);

        // Act
        var result = await _sut.CreateTaskForJournalAsync(journal, taskName);

        // Assert
        Assert.Equal(expectedTaskId, result);

        _apifyClientMock.Verify(x => x.CreateTaskAsync(taskName, It.IsAny<List<string>>(), 1, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CreateGroupTaskAsync_WhenSuccessful_ReturnsTaskId()
    {
        // Arrange
        var journals = new List<JournalScheduleInfo>
        {
            new JournalScheduleInfoBuilder().WithName("Nature Medicine").WithUrl("https://nature.com/nm").Build()
        };
        var taskName = Fake.GetRandomString(20);
        var expectedTaskId = Fake.GetRandomString(10);

        _apifyClientMock.Setup(x => x.CreateTaskAsync(taskName, It.IsAny<List<string>>(), 1, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedTaskId);

        // Act
        var result = await _sut.CreateGroupTaskAsync(journals, taskName);

        // Assert
        Assert.Equal(expectedTaskId, result);

        _apifyClientMock.Verify(x => x.CreateTaskAsync(
            taskName,
            It.Is<List<string>>(urls => urls.Count == 1 && urls[0] == journals[0].Url),
            1,
            It.IsAny<CancellationToken>()), Times.Once);

        VerifyLogCalled(LogLevel.Information, "Created Apify group task");
    }

    [Fact]
    public async Task CreateGroupTaskAsync_WhenApifyClientThrows_ThrowsInvalidOperationException()
    {
        // Arrange
        var journals = new List<JournalScheduleInfo>
        {
            new JournalScheduleInfoBuilder().WithName("Nature Medicine").WithUrl("https://nature.com/nm").Build()
        };
        var taskName = Fake.GetRandomString(20);
        var originalException = new Exception("Apify API error");

        _apifyClientMock.Setup(x => x.CreateTaskAsync(It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(originalException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _sut.CreateGroupTaskAsync(journals, taskName));

        Assert.Contains($"Failed to create Apify group task '{taskName}'", exception.Message);
        Assert.Equal(originalException, exception.InnerException);

        VerifyLogCalled(LogLevel.Error, "Failed to create Apify group task");
    }

    [Fact]
    public async Task CreateGroupTaskAsync_WithMultipleJournals_HandlesCorrectly()
    {
        // Arrange
        var journals = new List<JournalScheduleInfo>
        {
            new JournalScheduleInfoBuilder().WithName("Journal of Clinical Oncology (JCO)").WithUrl("https://test.org").Build(),
            new JournalScheduleInfoBuilder().WithName("Nature Medicine").WithUrl("https://nature.com/nm").Build()
        };
        var taskName = Fake.GetRandomString(25);
        var expectedTaskId = Fake.GetRandomString(10);

        _apifyClientMock.Setup(x => x.CreateTaskAsync(taskName, It.IsAny<List<string>>(), 2, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedTaskId);

        // Act
        var result = await _sut.CreateGroupTaskAsync(journals, taskName);

        // Assert
        Assert.Equal(expectedTaskId, result);

        _apifyClientMock.Verify(x => x.CreateTaskAsync(
            taskName,
            It.Is<List<string>>(urls => urls.Count == 2 && urls.Contains("https://test.org") && urls.Contains("https://nature.com/nm")),
            2,
            It.IsAny<CancellationToken>()), Times.Once);

        VerifyLogCalled(LogLevel.Information, "Created Apify group task");
    }

    private void VerifyLogCalled(LogLevel level, string message)
    {
        _loggerMock.Verify(
            x => x.Log(
                level,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }
}
