﻿using FuzzySharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl
{
    internal class CountryOfOccurrenceChecker : IExtractionValidator
    {
        private List<string> _countriesList = new();
        private readonly ICountryRepository _countryRepository;
        private readonly int _fuzzySharpCountryMatchThreshold;
        private readonly int _fuzzySharpCountryInitialismThreshold;
        private readonly ILogger<CountryOfOccurrenceChecker> _logger;
        private Task? _loadingTask;

        public CountryOfOccurrenceChecker(ILogger<CountryOfOccurrenceChecker> logger, ICountryRepository countryRepository, IConfiguration configuration)
        {
            _fuzzySharpCountryMatchThreshold = configuration.GetValue<int>("DataExtraction:FuzzySharpCountryMatchThreshold");
            _fuzzySharpCountryInitialismThreshold = configuration.GetValue<int>("DataExtraction:FuzzySharpCountryInitialismThreshold");
            _countryRepository = countryRepository;
            _logger = logger;

        }

        public bool IsValid(ExtractedReference extractedReference)
        {
            CountriesInitialized();
            var countryOfOccurrence = extractedReference.CountryOfOccurrence.Value;
            _logger.LogInformation("Extracted Country of Occurrence :{CountryOfOccurrence}", countryOfOccurrence);

            if (_countriesList.Count == 0)
            {
                _logger.LogWarning("Countries count is zero");
                return false;
            }

            var countryOfOccurrenceUpper = countryOfOccurrence.ToUpper();
            var countriesOfOccurrenceUpper = _countriesList.Select(x => x.ToUpper());

            var fuzzyMatchSuccess = countriesOfOccurrenceUpper.Any(x =>
            {
                var standardRatio = Fuzz.Ratio(countryOfOccurrenceUpper, x);
                var initialismRatio = Fuzz.TokenInitialismRatio(countryOfOccurrenceUpper, x);
                var standardMatch = standardRatio > _fuzzySharpCountryMatchThreshold;
                var initialismMatch = initialismRatio > _fuzzySharpCountryInitialismThreshold;
                if (standardMatch || initialismMatch)
                {
                    _logger.LogDebug("Country match found: '{Input}' -> '{Target}' (Standard: {StandardRatio}, Initialism: {InitialismRatio})",
                        countryOfOccurrenceUpper, x, standardRatio, initialismRatio);
                    return true;
                }
                return false;
            });

            if (!fuzzyMatchSuccess)
            {
                _logger.LogWarning("Country of Occurrence: {CountryOfOccurrence} could not be matched.", countryOfOccurrenceUpper);
            }

            return fuzzyMatchSuccess;
        }
        private void CountriesInitialized()
        {
            if (_countriesList.Count > 0)
                return;
            _loadingTask ??= PopulateCountriesName();
            _loadingTask.GetAwaiter().GetResult();

        }

        private async Task PopulateCountriesName()
        {
            _countriesList = (await _countryRepository.GetNames()).ToList();
        }

    }
}
