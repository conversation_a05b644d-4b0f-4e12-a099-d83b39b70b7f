﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using Xunit;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests
{

    public class CountryOfOccurrenceMatchingCheckerTests
    {
        private readonly CountryOfOccurrenceChecker _countryOfOccurrenceMatchingChecker;
        private readonly Mock<ILogger<CountryOfOccurrenceChecker>> _mockLogger = new();
        private readonly Mock<ICountryRepository> _countriesRepository = new();

        public CountryOfOccurrenceMatchingCheckerTests()
        {
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?> { { "DataExtraction:FuzzySharpCountryMatchThreshold", "90" }, { "DataExtraction:FuzzySharpCountryInitialismThreshold", "60" } })
                .Build();
            _countriesRepository.Setup(x => x.GetNames()).ReturnsAsync(CountryNames());
            _countryOfOccurrenceMatchingChecker = new CountryOfOccurrenceChecker(_mockLogger.Object, _countriesRepository.Object, configuration);
        }
        [Fact]
        public void CountryOfOccurrenceMatcher_MatchFound_ReturnsTrue()
        {
            //Arrange
            var extractedReference = new ExtractedReference
            {
                Title = null!,
                Abstract = null!,
                Authors = [],
                Affiliations = [],
                Doi = null!,
                IssueNumber = null!,
                Volume = null!,
                Issn = null!,
                Year = null!,
                Pages = null!,
                CountryOfOccurrence = new CountryOfOccurrence() { Confidence = 1, Value = "United Kingdom" },
                Keywords = null!,
                JournalTitle = null!
            };

            // Act
            var result = _countryOfOccurrenceMatchingChecker.IsValid(extractedReference);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void CountryOfOccurrenceMatcher_MatchNotFound_ReturnsFalse()
        {
            //Arrange
            var extractedReference = new ExtractedReference
            {
                Title = null!,
                Abstract = null!,
                Authors = [],
                Affiliations = [],
                Doi = null!,
                IssueNumber = null!,
                Volume = null!,
                Issn = null!,
                Year = null!,
                Pages = null!,
                CountryOfOccurrence = new CountryOfOccurrence() { Confidence = 1, Value = "England" },
                Keywords = null!,
                JournalTitle = null!
            };

            // Act
            var result = _countryOfOccurrenceMatchingChecker.IsValid(extractedReference);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CountryOfOccurrenceMatcher_MatchFound_ReturnsTrue_For_WhenFuzzyTokenInitialismRatioMatchesCountryName()
        {
            //Arrange
            var extractedReference = new ExtractedReference
            {
                Title = null!,
                Abstract = null!,
                Authors = [],
                Affiliations = [],
                Doi = null!,
                IssueNumber = null!,
                Volume = null!,
                Issn = null!,
                Year = null!,
                Pages = null!,
                CountryOfOccurrence = new CountryOfOccurrence() { Confidence = 1, Value = "UK" },
                Keywords = null!,
                JournalTitle = null!
            };

            // Act
            var result = _countryOfOccurrenceMatchingChecker.IsValid(extractedReference);

            // Assert
            Assert.True(result);
        }
        private static List<string> CountryNames()
        {
            return new List<string>
            {
                "United Kingdom",
                "United States",
                "Portugal",
                "Germany",
                "India",
                "China",
                "France"
            };
        }

    }
}
