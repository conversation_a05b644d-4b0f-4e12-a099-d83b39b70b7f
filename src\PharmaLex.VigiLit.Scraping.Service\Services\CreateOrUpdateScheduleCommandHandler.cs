﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Helpers;
using Phlex.Core.Apify.Interfaces;
using PharmaLex.VigiLit.Scraping.Client.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Services
{
    public class CreateOrUpdateScheduleCommandHandler : ICreateOrUpdateScheduleCommandHandler
    {
        private readonly ILogger<CreateOrUpdateScheduleCommandHandler> _logger;
        private readonly IApifyTaskService _taskService;
        private readonly IApifyScheduleService _scheduleService;
        private readonly IApifyWebhookService _webhookService;
        private readonly IScrapingConfigurationService _configurationService;

        public CreateOrUpdateScheduleCommandHandler(
            ILogger<CreateOrUpdateScheduleCommandHandler> logger,
            IApifyClient apifyClient,
            IApifyTaskService taskService,
            IApifyScheduleService scheduleService,
            IApifyWebhookService webhookService,
            IScrapingConfigurationService configurationService)
        {
            _logger = logger;
            _taskService = taskService;
            _scheduleService = scheduleService;
            _webhookService = webhookService;
            _configurationService = configurationService;
        }

        public async Task Consume(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("CreateOrUpdateScheduleCommandHandler:Consume:{Journal}", LogSanitizer.Sanitize(command.Journal.Name));

            try
            {
                if (command.OperationType == JournalOperationType.Create)
                {
                    await CreateSchedule(command, cancellationToken);
                }
                else if (command.OperationType == JournalOperationType.Update)
                {
                    await UpdateSchedule(command, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to create or update Apify schedule: {ex.Message}";
                _logger.LogError(ex, LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
                throw new InvalidOperationException(errorMessage, ex);
            }

            _logger.LogInformation("CreateOrUpdateJournalCommandHandler:Consume: Completed.");
        }

        private async Task UpdateSchedule(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        private async Task CreateSchedule(CreateOrUpdateScheduleCommand command, CancellationToken cancellationToken)
        {
            var schedules = await _scheduleService.GetSchedulesAsync(cancellationToken);
            var schedule = schedules?.Data.Items.Find(x => x.CronExpression == command.Journal.CronExpression);
            var taskName = $"vigilit-{ScrapingHelper.SanitizeApifyName(command.Journal.Name)}-{command.Journal.Id}";
            var scheduleName = ScrapingHelper.GenerateUniqueName("schedule", command.Journal.CronExpression);

            var journal = new JournalScheduleInfo()
            {
                Id = command.Journal.Id,
                Name = command.Journal.Name,
                Url = command.Journal.Url,
                CronExpression = command.Journal.CronExpression
            };

            // Create task
            var taskId = await _taskService.CreateTaskForJournalAsync(journal, taskName, cancellationToken);
            if (string.IsNullOrEmpty(taskId))
            {
                var errorMessage = $"Failed to create task for journal '{command.Journal.Name}': Task ID was null or empty";
                _logger.LogWarning(LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
                return;
            }
            if (schedule != null)
            {
                // The schedule exists - add new task to the existing schedule
                await _scheduleService.UpdateScheduleAsync(schedule.Id, taskId, cancellationToken);
            }
            else
            {
                // The schedule doesn't exist - create schedule and webhook
                await CreateScheduleAndWebhook(command, scheduleName, taskId, cancellationToken);
            }
        }

        private async Task CreateScheduleAndWebhook(CreateOrUpdateScheduleCommand command, string scheduleName, string taskId, CancellationToken cancellationToken)
        {
            // Create schedule
            await _scheduleService.CreateScheduleForTaskAsync(taskId, scheduleName, command.Journal.CronExpression, cancellationToken);

            // Create webhook
            var webhookUrl = _configurationService.GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                var errorMessage = $"Failed to create task for journal '{command.Journal.Name}': Webhook URL is missing";
                _logger.LogWarning(LoggingConstants.CreateOrUpdateScheduleErrorTemplate, errorMessage);
                return;
            }
            await _webhookService.CreateWebhookForTaskAsync(taskId, webhookUrl, cancellationToken);
        }
    }
}
