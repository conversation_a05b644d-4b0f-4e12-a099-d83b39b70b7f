# Implementation Summary: Using GetKeyValueStoreFileInfoAsync for Document-Journal Association

## Overview
This implementation modifies the current document-to-journal association logic to use the `GetKeyValueStoreFileInfoAsync` method instead of the existing blob metadata approach.

## Changes Made

### 1. ApifyMetadataService.cs
- **Added IApifyClient dependency**: The service now accepts `IApifyClient` in its constructor to enable key-value store operations.
- **New method**: `GroupFilesByJournalUsingKeyValueStoreAsync(string runId, CancellationToken cancellationToken = default)`
  - This method retrieves file information from Apify's key-value store
  - Groups files by journal based on metadata from the key-value store
  - Includes fallback logic and comprehensive error handling
- **KeyValueStoreFileInfo class**: Added placeholder class definition (should be moved to Phlex.Core.Apify package)
- **Enhanced journal key extraction**: `GetJournalKeyFromFileInfo` method processes key-value store file metadata

### 2. IApifyMetadataService.cs
- **Added interface method**: `GroupFilesByJournalUsingKeyValueStoreAsync` to support the new functionality

### 3. ApifyNotification.cs
- **Modified CreateAndSendExtractDataCommand**: 
  - Now attempts to use the new key-value store method first
  - Falls back to the original blob metadata approach if the new method fails
  - Includes comprehensive logging for both success and fallback scenarios

### 4. Test Updates
- **ApifyMetadataServiceTest.cs**: Updated constructor to include IApifyClient mock, added tests for new method
- **ApifyNotificationServiceTest.cs**: Added mocking for the new method and fallback behavior testing

## Key Features

### Fallback Mechanism
The implementation includes a robust fallback mechanism:
1. First attempts to use `GroupFilesByJournalUsingKeyValueStoreAsync`
2. If that fails, falls back to the original `GroupFilesByJournalAsync` method
3. Logs appropriate messages for both success and fallback scenarios

### Journal Key Extraction Logic
The new method supports multiple metadata sources for journal identification:
- `JournalId` + `JournalName` combination
- URL-based grouping (extracts host from URL)
- `ActorTaskId` fallback
- Filename pattern matching
- Default grouping for unidentifiable files

### Error Handling
- Comprehensive try-catch blocks around key operations
- Individual file processing errors don't stop the entire operation
- Failed files are added to a "Default" group
- Detailed logging for troubleshooting

## Important Notes

### Placeholder Implementation
The current implementation includes a placeholder for `GetKeyValueStoreFileInfoAsync` because:
1. The method doesn't exist in the current `IApifyClient` interface
2. The `KeyValueStoreFileInfo` class needs to be defined in the Phlex.Core.Apify package
3. The actual API integration needs to be implemented

### Next Steps for Full Implementation
1. **Add to Phlex.Core.Apify package**:
   - Add `GetKeyValueStoreFileInfoAsync` method to `IApifyClient` interface
   - Implement the method to make actual Apify API calls
   - Define `KeyValueStoreFileInfo` class with proper properties

2. **Remove placeholder code**:
   - Remove the local `KeyValueStoreFileInfo` class definition
   - Remove the placeholder `GetKeyValueStoreFileInfoAsync` method
   - Update using statements to reference the Phlex.Core.Apify types

3. **Configuration**:
   - Ensure proper Apify API credentials and endpoints are configured
   - Test with actual Apify key-value store data

## Benefits
- **Improved data source**: Uses Apify's key-value store which may contain richer metadata
- **Backward compatibility**: Maintains existing functionality through fallback mechanism
- **Better error handling**: More robust error handling and logging
- **Testable**: Comprehensive unit tests ensure reliability
- **Extensible**: Easy to add new journal identification strategies

## Testing
All existing tests pass, and new tests have been added to cover:
- New method functionality (placeholder behavior)
- Fallback mechanism
- Error handling scenarios
- Integration with existing ApifyNotification workflow
