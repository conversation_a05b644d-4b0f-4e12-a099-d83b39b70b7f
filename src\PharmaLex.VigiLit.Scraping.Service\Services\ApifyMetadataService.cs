using Azure.Storage.Blobs;
using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

// Placeholder class for KeyValueStoreFileInfo
// This should be defined in the Phlex.Core.Apify package
public class KeyValueStoreFileInfo
{
    public string? Key { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
    public long? Size { get; set; }
    public DateTime? CreatedAt { get; set; }
    public string? ContentType { get; set; }
}

public class ApifyMetadataService : IApifyMetadataService
{
    private readonly ILogger<ApifyMetadataService> _logger;
    private readonly IApifyClient _apifyClient;

    public ApifyMetadataService(ILogger<ApifyMetadataService> logger, IApifyClient apifyClient)
    {
        _logger = logger;
        _apifyClient = apifyClient;
    }

    public Dictionary<string, string> ExtractMetadataFromWebhookPayload(ApifyWebhookPayload runData)
    {
        var metadata = new Dictionary<string, string>();

        if (!string.IsNullOrWhiteSpace(runData.resource?.actorTaskId))
        {
            metadata["ActorTaskId"] = runData.resource.actorTaskId;
        }

        if (!string.IsNullOrWhiteSpace(runData.resource?.defaultDatasetId))
        {
            metadata["DatasetId"] = runData.resource.defaultDatasetId;
        }

        if (!string.IsNullOrWhiteSpace(runData.resource?.defaultKeyValueStoreId))
        {
            metadata["KeyValueStoreId"] = runData.resource.defaultKeyValueStoreId;
        }

        metadata["ProcessedAt"] = DateTime.UtcNow.ToString("O");

        return metadata;
    }

    public async Task<Dictionary<string, List<string>>> GroupFilesByJournalAsync(IEnumerable<string> blobPaths, BlobContainerClient containerClient)
    {
        var filesByJournal = new Dictionary<string, List<string>>();

        foreach (var blobPath in blobPaths)
        {
            try
            {
                var metadata = await ReadBlobMetadataAsync(blobPath, containerClient);
                var journalKey = GetJournalKey(metadata);

                if (!filesByJournal.TryGetValue(journalKey, out List<string>? value))
                {
                    value = [];
                    filesByJournal[journalKey] = value;
                }

                value.Add(blobPath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to read metadata for blob {BlobPath}, adding to default group", blobPath);

                const string defaultKey = "Default";
                if (!filesByJournal.TryGetValue(defaultKey, out List<string>? value))
                {
                    value = [];
                    filesByJournal[defaultKey] = value;
                }

                value.Add(blobPath);
            }
        }

        if (filesByJournal.Count == 0)
        {
            filesByJournal["Default"] = [.. blobPaths];
        }

        return filesByJournal;
    }

    public async Task<Dictionary<string, List<string>>> GroupFilesByJournalUsingKeyValueStoreAsync(string runId, CancellationToken cancellationToken = default)
    {
        var filesByJournal = new Dictionary<string, List<string>>();

        try
        {
            _logger.LogInformation("Getting key-value store file info for run ID: {RunId}", runId);

            // Note: Since GetKeyValueStoreFileInfoAsync doesn't exist in the current IApifyClient interface,
            // we'll simulate the functionality by creating a method that would work with the expected
            // KeyValueStoreFileInfo structure. In a real implementation, this method would need to be
            // added to the Phlex.Core.Apify package or we would need to use alternative Apify API calls.

            // For now, we'll create a placeholder implementation that demonstrates the intended functionality
            var keyValueStoreFiles = await GetKeyValueStoreFileInfoAsync(runId, cancellationToken);

            if (keyValueStoreFiles == null || !keyValueStoreFiles.Any())
            {
                _logger.LogWarning("No key-value store files found for run ID: {RunId}", runId);
                return filesByJournal;
            }

            foreach (var fileInfo in keyValueStoreFiles)
            {
                try
                {
                    var journalKey = GetJournalKeyFromFileInfo(fileInfo);
                    var fileName = fileInfo.Key ?? "unknown";

                    if (!filesByJournal.TryGetValue(journalKey, out List<string>? value))
                    {
                        value = [];
                        filesByJournal[journalKey] = value;
                    }

                    value.Add(fileName);

                    _logger.LogDebug("Associated file {FileName} with journal group {JournalKey}", fileName, journalKey);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process key-value store file info for file {FileName}, adding to default group", fileInfo.Key);

                    const string defaultKey = "Default";
                    if (!filesByJournal.TryGetValue(defaultKey, out List<string>? value))
                    {
                        value = [];
                        filesByJournal[defaultKey] = value;
                    }

                    value.Add(fileInfo.Key ?? "unknown");
                }
            }

            if (filesByJournal.Count == 0)
            {
                _logger.LogWarning("No files were successfully grouped for run ID: {RunId}, creating default group", runId);
                filesByJournal["Default"] = keyValueStoreFiles.Select(f => f.Key ?? "unknown").ToList();
            }

            _logger.LogInformation("Successfully grouped {FileCount} files into {GroupCount} journal groups for run ID: {RunId}",
                keyValueStoreFiles.Count(), filesByJournal.Count, runId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get key-value store file info for run ID: {RunId}", runId);
            throw;
        }

        return filesByJournal;
    }

    // Placeholder implementation for the GetKeyValueStoreFileInfoAsync method
    // This method should be implemented in the Phlex.Core.Apify package
    private async Task<List<KeyValueStoreFileInfo>> GetKeyValueStoreFileInfoAsync(string runId, CancellationToken cancellationToken = default)
    {
        // TODO: This is a placeholder implementation. In a real scenario, this method should:
        // 1. Be added to the IApifyClient interface in the Phlex.Core.Apify package
        // 2. Make actual API calls to Apify to retrieve key-value store file information
        // 3. Return actual KeyValueStoreFileInfo objects with proper metadata

        _logger.LogWarning("GetKeyValueStoreFileInfoAsync is not yet implemented in the Apify client. Using placeholder implementation.");

        // For demonstration purposes, return an empty list
        // In a real implementation, this would make HTTP calls to Apify's API
        await Task.Delay(100, cancellationToken); // Simulate async operation

        return new List<KeyValueStoreFileInfo>();
    }

    private async Task<Dictionary<string, string>> ReadBlobMetadataAsync(string blobPath, BlobContainerClient containerClient)
    {
        var blobClient = containerClient.GetBlobClient(blobPath);
        var properties = await blobClient.GetPropertiesAsync();
        return new Dictionary<string, string>(properties.Value.Metadata);
    }

    private static string GetJournalKey(Dictionary<string, string> metadata)
    {
        if (metadata.TryGetValue("JournalId", out var journalId) &&
            metadata.TryGetValue("JournalName", out var journalName))
        {
            return $"Journal_{journalId}_{journalName}";
        }

        if (metadata.TryGetValue("url", out var url) && !string.IsNullOrEmpty(url))
        {
            var host = ExtractHostFromUrl(url);
            return $"URL_{host}";
        }

        if (metadata.TryGetValue("ActorTaskId", out var taskId))
        {
            return $"Task_{taskId}";
        }

        return "Default";
    }

    private static string ExtractHostFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            return uri.Host;
        }
        catch
        {
            return url.Replace("://", "_").Replace("/", "_").Replace(".", "_");
        }
    }

    private static string GetJournalKeyFromFileInfo(KeyValueStoreFileInfo fileInfo)
    {
        try
        {
            // Try to extract journal information from file info properties
            if (fileInfo.Metadata != null)
            {
                var metadata = fileInfo.Metadata;

                if (metadata.TryGetValue("JournalId", out var journalId) &&
                    metadata.TryGetValue("JournalName", out var journalName))
                {
                    return $"Journal_{journalId}_{journalName}";
                }

                if (metadata.TryGetValue("url", out var url) && url != null)
                {
                    var host = ExtractHostFromUrl(url.ToString() ?? "");
                    return $"URL_{host}";
                }

                if (metadata.TryGetValue("ActorTaskId", out var taskId) && taskId != null)
                {
                    return $"Task_{taskId}";
                }
            }

            // Try to extract from file name or key if metadata is not available
            var fileName = fileInfo.Key ?? "";
            if (!string.IsNullOrEmpty(fileName))
            {
                // Try to extract journal info from filename patterns
                if (fileName.Contains("journal_"))
                {
                    var parts = fileName.Split('_');
                    if (parts.Length >= 2)
                    {
                        return $"Journal_{parts[1]}";
                    }
                }
            }

            return "Default";
        }
        catch (Exception)
        {
            return "Default";
        }
    }
}
