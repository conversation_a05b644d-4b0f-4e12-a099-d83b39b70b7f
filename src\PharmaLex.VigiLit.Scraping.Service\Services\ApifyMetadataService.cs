using Azure.Storage.Blobs;
using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Webhook.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyMetadataService : IApifyMetadataService
{
    private readonly ILogger<ApifyMetadataService> _logger;

    public ApifyMetadataService(ILogger<ApifyMetadataService> logger)
    {
        _logger = logger;
    }

    public Dictionary<string, string> ExtractMetadataFromWebhookPayload(ApifyWebhookPayload runData)
    {
        var metadata = new Dictionary<string, string>();

        if (!string.IsNullOrWhiteSpace(runData.resource?.actorTaskId))
        {
            metadata["ActorTaskId"] = runData.resource.actorTaskId;
        }

        if (!string.IsNullOrWhiteSpace(runData.resource?.defaultDatasetId))
        {
            metadata["DatasetId"] = runData.resource.defaultDatasetId;
        }

        if (!string.IsNullOrWhiteSpace(runData.resource?.defaultKeyValueStoreId))
        {
            metadata["KeyValueStoreId"] = runData.resource.defaultKeyValueStoreId;
        }

        metadata["ProcessedAt"] = DateTime.UtcNow.ToString("O");

        return metadata;
    }

    public async Task<Dictionary<string, List<string>>> GroupFilesByJournalAsync(IEnumerable<string> blobPaths, BlobContainerClient containerClient)
    {
        var filesByJournal = new Dictionary<string, List<string>>();

        foreach (var blobPath in blobPaths)
        {
            try
            {
                var metadata = await ReadBlobMetadataAsync(blobPath, containerClient);
                var journalKey = GetJournalKey(metadata);

                if (!filesByJournal.TryGetValue(journalKey, out List<string>? value))
                {
                    value = [];
                    filesByJournal[journalKey] = value;
                }

                value.Add(blobPath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to read metadata for blob {BlobPath}, adding to default group", blobPath);
                
                const string defaultKey = "Default";
                if (!filesByJournal.TryGetValue(defaultKey, out List<string>? value))
                {
                    value = [];
                    filesByJournal[defaultKey] = value;
                }

                value.Add(blobPath);
            }
        }

        if (filesByJournal.Count == 0)
        {
            filesByJournal["Default"] = [.. blobPaths];
        }

        return filesByJournal;
    }

    private async Task<Dictionary<string, string>> ReadBlobMetadataAsync(string blobPath, BlobContainerClient containerClient)
    {
        var blobClient = containerClient.GetBlobClient(blobPath);
        var properties = await blobClient.GetPropertiesAsync();
        return new Dictionary<string, string>(properties.Value.Metadata);
    }

    private static string GetJournalKey(Dictionary<string, string> metadata)
    {
        if (metadata.TryGetValue("JournalId", out var journalId) &&
            metadata.TryGetValue("JournalName", out var journalName))
        {
            return $"Journal_{journalId}_{journalName}";
        }

        if (metadata.TryGetValue("url", out var url) && !string.IsNullOrEmpty(url))
        {
            var host = ExtractHostFromUrl(url);
            return $"URL_{host}";
        }

        if (metadata.TryGetValue("ActorTaskId", out var taskId))
        {
            return $"Task_{taskId}";
        }

        return "Default";
    }

    private static string ExtractHostFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            return uri.Host;
        }
        catch
        {
            return url.Replace("://", "_").Replace("/", "_").Replace(".", "_");
        }
    }
}
