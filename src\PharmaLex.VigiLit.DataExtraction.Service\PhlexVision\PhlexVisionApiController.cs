﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Auditing.Client;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.Logging;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using System.Text.Json;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

[Route("api/[controller]")]
[ApiController]
[AllowAnonymous]
public class PhlexVisionApiController(
                            ICallbackHandler callbackHandler,
                            ILogger<PhlexVisionApiController> logger,
                            IAuditClient auditClient) : ControllerBase
{
    private const string CompletedWithFailures = "Completed with failures";

    [HttpGet("DownloadDocument/{documentId}/{filename}/")]
    public async Task<IActionResult> DownloadDocument([FromRoute] DownloadDocumentsCommandRequest downloadRequest,
        string documentId, string filename)
    {
        logger.LogInformation("PhlexVisionApiController: DownloadDocument: {CorrelationId} : {DocumentId} : {FileName}",
            LogSanitizer.Sanitize(downloadRequest.CorrelationId ?? "Null CorrelationId"),
            LogSanitizer.Sanitize(downloadRequest.DocumentId ?? "Null DocumentId"),
            LogSanitizer.Sanitize(downloadRequest.FileName ?? "Null FileName"));

        if (!TryParse(downloadRequest, out var correlationId))
        {
            await SendStatusChangedEvent(correlationId, CompletedWithFailures);
            throw new ArgumentException($"Correlation Id is empty for {downloadRequest.FileName}");
        }


        return new FileStreamResult(await callbackHandler.GetDocumentStream(correlationId), "application/pdf")
        {
            FileDownloadName = filename,
        };
    }

    [HttpPost("success/{documentId}/")]
    [IgnoreAntiforgeryToken]
    public async Task<IActionResult> Success([FromRoute][FromBody] SuccessCallbackRequest request, string documentId)
    {
        logger.LogInformation("PhlexVisionApiController: Success: {CorrelationId}",
            LogSanitizer.Sanitize(request.CorrelationId ?? "Null CorrelationId"));


        if (!TryParse(request, out var correlationId))
        {
            await BadRequestWithStatusUpdate(correlationId);
        }
        var (contextInfo, extractedMetaData) = await GetExtractedMetaData(HttpContext.Request.Body);
        await callbackHandler.Success(correlationId, extractedMetaData, contextInfo);

        return Ok();
    }

    [HttpPost("error/{documentId}/")]
    [IgnoreAntiforgeryToken]
    public async Task<IActionResult> Error([FromRoute][FromBody] ErrorCallbackRequest request, string documentId)
    {
        logger.LogInformation("PhlexVisionApiController: Error: {CorrelationId}",
            LogSanitizer.Sanitize(request.CorrelationId ?? "Null CorrelationId"));
        
        if (!TryParse(request, out var correlationId))
        {
            await BadRequestWithStatusUpdate(correlationId);
        }

        await SendStatusChangedEvent(correlationId, CompletedWithFailures);
        await callbackHandler.Error(correlationId);

        return Ok();
    }

    private async Task<BadRequestResult> BadRequestWithStatusUpdate(Guid correlationId)
    {
        await SendStatusChangedEvent(correlationId, CompletedWithFailures);
        return base.BadRequest();
    }

    private static bool TryParse(IHasCorrelationId request, out Guid correlationId)
    {
        return Guid.TryParse(request.CorrelationId, out correlationId);
    }

    private readonly JsonSerializerOptions _caseInsensitiveSerializerOptions = new JsonSerializerOptions
    {
        PropertyNameCaseInsensitive = true
    };

    private async Task<(ContextInfo, ExtractedMetadata)> GetExtractedMetaData(Stream responseBodyStream)
    {
        using var reader = new StreamReader(responseBodyStream);
        var body = await reader.ReadToEndAsync();
        logger.LogInformation("{Body}", LogSanitizer.Sanitize(body));

        var root = JsonSerializer.Deserialize<Rootobject>(body, _caseInsensitiveSerializerOptions);

        if (root == null) throw new ArgumentException("Could not deserialize response body.");

        var extractedMetaData = root.ExtractedMetadata;

        var language = root.Languages?.Detected?.Length > 0 ? root.Languages.Detected[0] : string.Empty;

        var contextInfo = new ContextInfo()
        {
            Language = language,
            RawTranslatedText = root.TranslatedText,
        };

        return (contextInfo, extractedMetaData);
    }

    private async Task SendStatusChangedEvent(Guid correlationId, string statusMessage)
    {
        var statusChangedEvent = new StatusChangedEvent(nameof(ImportType.File), correlationId,
            DateTime.UtcNow, "<EMAIL>", statusMessage);
        await auditClient.Send(statusChangedEvent);
    }

}
